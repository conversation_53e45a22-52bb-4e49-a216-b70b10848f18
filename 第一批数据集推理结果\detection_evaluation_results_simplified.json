{"evaluation_config": {"gt_root": "/home/<USER>/Qwen/Qwen2.5-VL/tia<PERSON><PERSON><PERSON>_datasets_val/labels", "pred_root": "/home/<USER>/Qwen/Qwen2.5-VL/weak_target_finetune/alltestlabels", "iou_threshold": 0.3, "class_names": ["drone", "car", "ship", "bus", "pedestrian", "cyclist"], "averaging_method": "direct_video_level", "averaging_description": "直接基于视频总体指标进行平均", "consistency_iou_threshold": 0.3, "stability_threshold": 0.8, "temporal_consistency_rules": {"targets_le5": "要求所有目标检出，且类别正确，且IoU > 0.3", "targets_gt5": "要求80%目标检出，且类别正确，且IoU > 0.3"}}, "video_results": {"data_01": {"frames_processed": 751, "frames_gt": 751, "frames_pred": 751, "metrics": {"0": {"class_name": "drone", "tp": 621, "fp": 157, "fn": 130, "recall": 0.8268974700399467, "precision": 0.7982005141388174, "false_alarm_rate": 0.20179948586118257, "f1": 0.8122956180510137, "total_gt": 751, "total_pred": 778}, "overall": {"tp": 621, "fp": 157, "fn": 130, "recall": 0.8268974700399467, "precision": 0.7982005141388174, "false_alarm_rate": 0.20179948586118257, "f1": 0.8122956180510137, "total_gt": 751, "total_pred": 778, "averaging_method": "macro", "valid_classes_count": 1, "valid_classes": [0], "temporal_consistency": 0.8268974700399467, "consistent_frames": 621, "total_frames": 751, "frames_with_targets_le5": 751, "consistent_frames_le5": 621, "frames_with_targets_gt5": 0, "consistent_frames_gt5": 0}}}, "data_02": {"frames_processed": 400, "frames_gt": 499, "frames_pred": 400, "metrics": {"0": {"class_name": "drone", "tp": 180, "fp": 100, "fn": 219, "recall": 0.45112781954887216, "precision": 0.6428571428571429, "false_alarm_rate": 0.35714285714285715, "f1": 0.5294117647058824, "total_gt": 399, "total_pred": 280}, "overall": {"tp": 180, "fp": 100, "fn": 219, "recall": 0.45112781954887216, "precision": 0.6428571428571429, "false_alarm_rate": 0.35714285714285715, "f1": 0.5294117647058824, "temporal_consistency": 0.45, "consistent_frames": 180, "total_frames": 400, "frames_with_targets_le5": 399, "consistent_frames_le5": 180}}}, "data_03": {"frames_processed": 500, "frames_gt": 500, "frames_pred": 500, "metrics": {"0": {"class_name": "drone", "tp": 343, "fp": 209, "fn": 157, "recall": 0.686, "precision": 0.6213768115942029, "false_alarm_rate": 0.3786231884057971, "f1": 0.6520912547528517, "total_gt": 500, "total_pred": 552}, "overall": {"tp": 343, "fp": 209, "fn": 157, "recall": 0.686, "precision": 0.6213768115942029, "false_alarm_rate": 0.3786231884057971, "f1": 0.6520912547528517, "total_gt": 500, "total_pred": 552, "averaging_method": "macro", "valid_classes_count": 1, "valid_classes": [0], "temporal_consistency": 0.686, "consistent_frames": 343, "total_frames": 500, "frames_with_targets_le5": 500, "consistent_frames_le5": 343, "frames_with_targets_gt5": 0, "consistent_frames_gt5": 0}}}, "data_04": {"frames_processed": 500, "frames_gt": 500, "frames_pred": 500, "metrics": {"0": {"class_name": "drone", "tp": 318, "fp": 145, "fn": 182, "recall": 0.636, "precision": 0.6868250539956804, "false_alarm_rate": 0.3131749460043196, "f1": 0.6604361370716512, "total_gt": 500, "total_pred": 463}, "overall": {"tp": 318, "fp": 145, "fn": 182, "recall": 0.636, "precision": 0.6868250539956804, "false_alarm_rate": 0.3131749460043196, "f1": 0.6604361370716512, "total_gt": 500, "total_pred": 463, "averaging_method": "macro", "valid_classes_count": 1, "valid_classes": [0], "temporal_consistency": 0.636, "consistent_frames": 318, "total_frames": 500, "frames_with_targets_le5": 500, "consistent_frames_le5": 318, "frames_with_targets_gt5": 0, "consistent_frames_gt5": 0}}}, "data_05": {"frames_processed": 119, "frames_gt": 599, "frames_pred": 119, "metrics": {"0": {"class_name": "drone", "tp": 107, "fp": 80, "fn": 131, "recall": 0.4495798319327731, "precision": 0.572192513368984, "false_alarm_rate": 0.427807486631016, "f1": 0.5023474178403756, "total_gt": 238, "total_pred": 187}, "overall": {"tp": 107, "fp": 80, "fn": 131, "recall": 0.4495798319327731, "precision": 0.572192513368984, "false_alarm_rate": 0.427807486631016, "f1": 0.5023474178403756, "temporal_consistency": 0.453781512605042, "consistent_frames": 54, "total_frames": 119, "frames_with_targets_le5": 119, "consistent_frames_le5": 54}}}, "data_06": {"frames_processed": 80, "frames_gt": 400, "frames_pred": 80, "metrics": {"0": {"class_name": "drone", "tp": 36, "fp": 40, "fn": 44, "recall": 0.45, "precision": 0.47368421052631576, "false_alarm_rate": 0.5263157894736842, "f1": 0.4615384615384615, "total_gt": 80, "total_pred": 76}, "overall": {"tp": 36, "fp": 40, "fn": 44, "recall": 0.45, "precision": 0.47368421052631576, "false_alarm_rate": 0.5263157894736842, "f1": 0.4615384615384615, "temporal_consistency": 0.45, "consistent_frames": 36, "total_frames": 80, "frames_with_targets_le5": 80, "consistent_frames_le5": 36}}}, "data_07": {"frames_processed": 500, "frames_gt": 500, "frames_pred": 500, "metrics": {"0": {"class_name": "drone", "tp": 458, "fp": 147, "fn": 42, "recall": 0.916, "precision": 0.7570247933884298, "false_alarm_rate": 0.24297520661157024, "f1": 0.8289592760180995, "total_gt": 500, "total_pred": 605}, "overall": {"tp": 458, "fp": 147, "fn": 42, "recall": 0.916, "precision": 0.7570247933884298, "false_alarm_rate": 0.24297520661157024, "f1": 0.8289592760180995, "total_gt": 500, "total_pred": 605, "averaging_method": "macro", "valid_classes_count": 1, "valid_classes": [0], "temporal_consistency": 0.916, "consistent_frames": 458, "total_frames": 500, "frames_with_targets_le5": 500, "consistent_frames_le5": 458, "frames_with_targets_gt5": 0, "consistent_frames_gt5": 0}}}, "data_08": {"frames_processed": 500, "frames_gt": 500, "frames_pred": 500, "metrics": {"0": {"class_name": "drone", "tp": 152, "fp": 496, "fn": 348, "recall": 0.304, "precision": 0.2345679012345679, "false_alarm_rate": 0.7654320987654322, "f1": 0.26480836236933797, "total_gt": 500, "total_pred": 648}, "overall": {"tp": 152, "fp": 496, "fn": 348, "recall": 0.304, "precision": 0.2345679012345679, "false_alarm_rate": 0.7654320987654322, "f1": 0.26480836236933797, "total_gt": 500, "total_pred": 648, "averaging_method": "macro", "valid_classes_count": 1, "valid_classes": [0], "temporal_consistency": 0.304, "consistent_frames": 152, "total_frames": 500, "frames_with_targets_le5": 500, "consistent_frames_le5": 152, "frames_with_targets_gt5": 0, "consistent_frames_gt5": 0}}}, "data_09": {"frames_processed": 2000, "frames_gt": 2000, "frames_pred": 2000, "metrics": {"0": {"class_name": "drone", "tp": 1996, "fp": 25, "fn": 4, "recall": 0.998, "precision": 0.987629886194953, "false_alarm_rate": 0.012370113805047045, "f1": 0.9927878637154938, "total_gt": 2000, "total_pred": 2021}, "overall": {"tp": 1996, "fp": 25, "fn": 4, "recall": 0.998, "precision": 0.987629886194953, "false_alarm_rate": 0.012370113805047045, "f1": 0.9927878637154938, "total_gt": 2000, "total_pred": 2021, "averaging_method": "macro", "valid_classes_count": 1, "valid_classes": [0], "temporal_consistency": 0.998, "consistent_frames": 1996, "total_frames": 2000, "frames_with_targets_le5": 2000, "consistent_frames_le5": 1996, "frames_with_targets_gt5": 0, "consistent_frames_gt5": 0}}}, "data_10": {"frames_processed": 580, "frames_gt": 580, "frames_pred": 580, "metrics": {"0": {"class_name": "drone", "tp": 372, "fp": 210, "fn": 208, "recall": 0.6413793103448275, "precision": 0.6391752577319587, "false_alarm_rate": 0.3608247422680413, "f1": 0.640275387263339, "total_gt": 580, "total_pred": 582}, "4": {"class_name": "pedestrian", "tp": 580, "fp": 0, "fn": 0, "recall": 1.0, "precision": 1.0, "false_alarm_rate": 0.0, "f1": 1.0, "total_gt": 580, "total_pred": 580}, "overall": {"tp": 952, "fp": 210, "fn": 208, "recall": 0.8206896551724138, "precision": 0.8195876288659794, "false_alarm_rate": 0.18041237113402064, "f1": 0.8201376936316696, "total_gt": 1160, "total_pred": 1162, "averaging_method": "macro", "valid_classes_count": 2, "valid_classes": [0, 4], "temporal_consistency": 0.6413793103448275, "consistent_frames": 372, "total_frames": 580, "frames_with_targets_le5": 580, "consistent_frames_le5": 372, "frames_with_targets_gt5": 0, "consistent_frames_gt5": 0}}}, "data_11": {"frames_processed": 600, "frames_gt": 600, "frames_pred": 600, "metrics": {"0": {"class_name": "drone", "tp": 417, "fp": 116, "fn": 183, "recall": 0.695, "precision": 0.7823639774859287, "false_alarm_rate": 0.21763602251407133, "f1": 0.736098852603707, "total_gt": 600, "total_pred": 533}, "4": {"class_name": "pedestrian", "tp": 600, "fp": 0, "fn": 0, "recall": 1.0, "precision": 1.0, "false_alarm_rate": 0.0, "f1": 1.0, "total_gt": 600, "total_pred": 600}, "overall": {"tp": 1017, "fp": 116, "fn": 183, "recall": 0.8474999999999999, "precision": 0.8911819887429644, "false_alarm_rate": 0.10881801125703566, "f1": 0.8680494263018534, "total_gt": 1200, "total_pred": 1133, "averaging_method": "macro", "valid_classes_count": 2, "valid_classes": [0, 4], "temporal_consistency": 0.695, "consistent_frames": 417, "total_frames": 600, "frames_with_targets_le5": 600, "consistent_frames_le5": 417, "frames_with_targets_gt5": 0, "consistent_frames_gt5": 0}}}, "data_12": {"frames_processed": 560, "frames_gt": 560, "frames_pred": 560, "metrics": {"0": {"class_name": "drone", "tp": 513, "fp": 78, "fn": 47, "recall": 0.9160714285714285, "precision": 0.868020304568528, "false_alarm_rate": 0.13197969543147203, "f1": 0.8913987836663771, "total_gt": 560, "total_pred": 591}, "4": {"class_name": "pedestrian", "tp": 1541, "fp": 22, "fn": 28, "recall": 0.9821542383683876, "precision": 0.9859245041586693, "false_alarm_rate": 0.01407549584133072, "f1": 0.9840357598978288, "total_gt": 1569, "total_pred": 1563}, "overall": {"tp": 2054, "fp": 100, "fn": 75, "recall": 0.949112833469908, "precision": 0.9269724043635986, "false_alarm_rate": 0.07302759563640138, "f1": 0.937717271782103, "total_gt": 2129, "total_pred": 2154, "averaging_method": "macro", "valid_classes_count": 2, "valid_classes": [0, 4], "temporal_consistency": 0.8910714285714286, "consistent_frames": 499, "total_frames": 560, "frames_with_targets_le5": 560, "consistent_frames_le5": 499, "frames_with_targets_gt5": 0, "consistent_frames_gt5": 0}}}, "data_13": {"frames_processed": 360, "frames_gt": 360, "frames_pred": 360, "metrics": {"1": {"class_name": "car", "tp": 33369, "fp": 784, "fn": 47, "recall": 0.9985934881493895, "precision": 0.9770444763271162, "false_alarm_rate": 0.022955523672883782, "f1": 0.9877014607290325, "total_gt": 33416, "total_pred": 34153}, "3": {"class_name": "bus", "tp": 1896, "fp": 7, "fn": 30, "recall": 0.9844236760124611, "precision": 0.9963215974776668, "false_alarm_rate": 0.0036784025223332018, "f1": 0.9903369025855315, "total_gt": 1926, "total_pred": 1903}, "4": {"class_name": "pedestrian", "tp": 5058, "fp": 1885, "fn": 691, "recall": 0.8798051835101757, "precision": 0.7285035287339766, "false_alarm_rate": 0.2714964712660234, "f1": 0.7970375039394895, "total_gt": 5749, "total_pred": 6943}, "5": {"class_name": "cyclist", "tp": 8160, "fp": 1504, "fn": 650, "recall": 0.9262202043132803, "precision": 0.8443708609271523, "false_alarm_rate": 0.1556291390728477, "f1": 0.883403702500812, "total_gt": 8810, "total_pred": 9664}, "overall": {"tp": 48483, "fp": 4180, "fn": 1418, "recall": 0.9472606379963266, "precision": 0.886560115866478, "false_alarm_rate": 0.11343988413352202, "f1": 0.9146198924387164, "total_gt": 49901, "total_pred": 52663, "averaging_method": "macro", "valid_classes_count": 4, "valid_classes": [1, 3, 4, 5], "temporal_consistency": 1.0, "consistent_frames": 360, "total_frames": 360, "frames_with_targets_le5": 0, "consistent_frames_le5": 0, "frames_with_targets_gt5": 360, "consistent_frames_gt5": 360}}}, "data_14": {"frames_processed": 1500, "frames_gt": 1500, "frames_pred": 1500, "metrics": {"0": {"class_name": "drone", "tp": 1500, "fp": 2, "fn": 0, "recall": 1.0, "precision": 0.9986684420772304, "false_alarm_rate": 0.0013315579227696217, "f1": 0.999333777481679, "total_gt": 1500, "total_pred": 1502}, "overall": {"tp": 1500, "fp": 2, "fn": 0, "recall": 1.0, "precision": 0.9986684420772304, "false_alarm_rate": 0.0013315579227696217, "f1": 0.999333777481679, "total_gt": 1500, "total_pred": 1502, "averaging_method": "macro", "valid_classes_count": 1, "valid_classes": [0], "temporal_consistency": 1.0, "consistent_frames": 1500, "total_frames": 1500, "frames_with_targets_le5": 1500, "consistent_frames_le5": 1500, "frames_with_targets_gt5": 0, "consistent_frames_gt5": 0}}}, "data_15": {"frames_processed": 1050, "frames_gt": 1050, "frames_pred": 1050, "metrics": {"0": {"class_name": "drone", "tp": 1729, "fp": 21, "fn": 153, "recall": 0.9187035069075452, "precision": 0.988, "false_alarm_rate": 0.01200000000000001, "f1": 0.9520925110132159, "total_gt": 1882, "total_pred": 1750}, "overall": {"tp": 1729, "fp": 21, "fn": 153, "recall": 0.9187035069075452, "precision": 0.988, "false_alarm_rate": 0.01200000000000001, "f1": 0.9520925110132159, "total_gt": 1882, "total_pred": 1750, "averaging_method": "macro", "valid_classes_count": 1, "valid_classes": [0], "temporal_consistency": 0.8695238095238095, "consistent_frames": 913, "total_frames": 1050, "frames_with_targets_le5": 1050, "consistent_frames_le5": 913, "frames_with_targets_gt5": 0, "consistent_frames_gt5": 0}}}}, "overall_results": {"overall": {"tp": 59946, "fp": 6028, "fn": 3294, "recall": 0.7207, "precision": 0.7428, "false_alarm_rate": 0.2572, "f1": 0.7302, "total_gt": 63240, "total_pred": 65974, "temporal_consistency": 0.7188, "total_frames": 10000, "total_consistent_frames": 8219, "spatiotemporal_stability": 0.6, "stable_videos": 9, "total_frames_with_targets_le5": 9639, "total_consistent_frames_le5": 7859}}}